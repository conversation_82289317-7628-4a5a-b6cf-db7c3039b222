import 'package:deewan/app/controllers/sittings/settingscontroller.dart';
import 'package:deewan/app/services/services_impl/appservices.dart';
import 'package:deewan/core/localization/localization_service.dart';
import 'package:deewan/core/network/api_client.dart';
import 'package:deewan/core/network/network_info.dart';
import 'package:deewan/core/storage/secure_storage.dart';
import 'package:deewan/core/storage/objectbox_store.dart';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;

class Initialbindings extends Binding {
  @override
  List<Bind> dependencies() => [
    // Core dependencies
    Bind.put(() => http.Client(), permanent: true),
    Bind.put(() => NetworkInfoImpl(), permanent: true),

    // Storage dependencies
    Bind.put(() => SecureStorageImpl(), permanent: true),
    Bind.put(() => ObjectBoxStore.instance, permanent: true),

    // Network dependencies
    Bind.put(() => ApiClient(client: Get.find<http.Client>()), permanent: true),

    // Core services (in dependency order)
    Bind.put(() => ObjectboxService(), permanent: true),
    Bind.put(() => LocalizationService(), permanent: true),
    Bind.put(() => SettingsService(), permanent: true),

    // Note: ErrorHandler and ValidationUtils are static utility classes
    // They don't need to be registered in DI as they have no state

    // TODO: Core repositories (when implemented)
    // Bind.lazyPut<AuthRepository>(() => AuthRepositoryImpl(
    //   apiClient: Get.find<ApiClient>(),
    //   secureStorage: Get.find<SecureStorage>(),
    // ), fenix: true),

    // Controllers - Only essential app-wide controllers
    Bind.put(() => SettingsController()),

    // TODO: Add these controllers when implemented:
    // Bind.put(() => AuthController(), permanent: true),      // User authentication state
    // Bind.put(() => NetworkController(), permanent: true),   // Internet connectivity monitoring
    // Bind.put(() => NavigationController(), permanent: true), // App navigation state
    // Bind.put(() => NotificationController(), permanent: true), // Push notifications
    // Bind.put(() => AppStateController(), permanent: true),  // App lifecycle management
  ];
}
